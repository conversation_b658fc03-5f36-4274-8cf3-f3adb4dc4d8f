<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SonarImage Fit Mode 改进测试</title>
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .test-container {
            border: 2px solid #333;
            background-color: #000;
            position: relative;
            margin: 10px auto;
            display: grid;
            place-items: center;
        }
        
        .image-container-fit {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .image-container-original {
            position: relative;
            max-width: 100%;
            max-height: 100%;
        }
        
        .test-canvas {
            display: block;
            border: 1px solid #ff0000;
        }
        
        .controls {
            text-align: center;
            margin: 10px 0;
        }
        
        button {
            padding: 8px 16px;
            margin: 0 5px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .info {
            text-align: center;
            margin: 5px 0;
            font-size: 12px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ccc;
            background-color: white;
        }
        
        .container-wide {
            width: 800px;
            height: 300px;
        }
        
        .container-tall {
            width: 300px;
            height: 600px;
        }
        
        .container-small {
            width: 400px;
            height: 300px;
        }
        
        .container-square {
            width: 400px;
            height: 400px;
        }
    </style>
</head>
<body>
    <h1>SonarImage Fit Mode 改进测试</h1>
    
    <div class="info">
        <p>图像尺寸: 640x480px (4:3 横纵比)</p>
        <p>红色边框显示 canvas 的实际边界</p>
    </div>
    
    <div class="controls">
        <button onclick="setAllFitMode()">所有容器 - Fit 模式</button>
        <button onclick="setAllOriginalMode()">所有容器 - Original 模式</button>
    </div>
    
    <!-- 测试场景1: 宽容器 -->
    <div class="test-section">
        <h3>测试1: 宽容器 (800x300) - 宽度足够，高度受限</h3>
        <div class="info">
            <p>当前模式: <span class="current-mode">fit</span></p>
            <p>期望: canvas 应该适应高度限制，宽度自动调整保持横纵比</p>
        </div>
        <div class="test-container container-wide">
            <div class="image-container image-container-fit">
                <canvas 
                    class="test-canvas"
                    width="640" 
                    height="480"
                    style="aspect-ratio: 640/480; max-width: 100%; max-height: 100%; width: auto; height: auto;"
                ></canvas>
            </div>
        </div>
    </div>
    
    <!-- 测试场景2: 高容器 -->
    <div class="test-section">
        <h3>测试2: 高容器 (300x600) - 高度足够，宽度受限</h3>
        <div class="info">
            <p>当前模式: <span class="current-mode">fit</span></p>
            <p>期望: canvas 应该适应宽度限制，高度自动调整保持横纵比</p>
        </div>
        <div class="test-container container-tall">
            <div class="image-container image-container-fit">
                <canvas 
                    class="test-canvas"
                    width="640" 
                    height="480"
                    style="aspect-ratio: 640/480; max-width: 100%; max-height: 100%; width: auto; height: auto;"
                ></canvas>
            </div>
        </div>
    </div>
    
    <!-- 测试场景3: 小容器 -->
    <div class="test-section">
        <h3>测试3: 小容器 (400x300) - 宽度和高度都受限</h3>
        <div class="info">
            <p>当前模式: <span class="current-mode">fit</span></p>
            <p>期望: canvas 应该完全适应容器，不超出边界</p>
        </div>
        <div class="test-container container-small">
            <div class="image-container image-container-fit">
                <canvas 
                    class="test-canvas"
                    width="640" 
                    height="480"
                    style="aspect-ratio: 640/480; max-width: 100%; max-height: 100%; width: auto; height: auto;"
                ></canvas>
            </div>
        </div>
    </div>
    
    <!-- 测试场景4: 正方形容器 -->
    <div class="test-section">
        <h3>测试4: 正方形容器 (400x400) - 不同横纵比</h3>
        <div class="info">
            <p>当前模式: <span class="current-mode">fit</span></p>
            <p>期望: canvas 应该适应容器，保持4:3横纵比，上下或左右留空</p>
        </div>
        <div class="test-container container-square">
            <div class="image-container image-container-fit">
                <canvas 
                    class="test-canvas"
                    width="640" 
                    height="480"
                    style="aspect-ratio: 640/480; max-width: 100%; max-height: 100%; width: auto; height: auto;"
                ></canvas>
            </div>
        </div>
    </div>

    <script>
        // 绘制测试图像到所有 canvas
        function drawTestImage(canvas) {
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            ctx.fillStyle = '#333';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制网格
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 1;
            for (let x = 0; x < canvas.width; x += 80) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y < canvas.height; y += 60) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
            
            // 绘制中心圆
            ctx.fillStyle = '#00ff00';
            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height / 2, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制文字
            ctx.fillStyle = '#ffffff';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('640x480', canvas.width / 2, canvas.height / 2 - 50);
            ctx.fillText('4:3 比例', canvas.width / 2, canvas.height / 2 + 50);
        }
        
        function setAllFitMode() {
            const containers = document.querySelectorAll('.image-container');
            const canvases = document.querySelectorAll('.test-canvas');
            const modeSpans = document.querySelectorAll('.current-mode');
            
            containers.forEach(container => {
                container.className = 'image-container image-container-fit';
            });
            
            canvases.forEach(canvas => {
                canvas.style.aspectRatio = '640/480';
                canvas.style.maxWidth = '100%';
                canvas.style.maxHeight = '100%';
                canvas.style.width = 'auto';
                canvas.style.height = 'auto';
            });
            
            modeSpans.forEach(span => {
                span.textContent = 'fit';
            });
        }
        
        function setAllOriginalMode() {
            const containers = document.querySelectorAll('.image-container');
            const canvases = document.querySelectorAll('.test-canvas');
            const modeSpans = document.querySelectorAll('.current-mode');
            
            containers.forEach(container => {
                container.className = 'image-container image-container-original';
            });
            
            canvases.forEach(canvas => {
                canvas.style.aspectRatio = '640/480';
                canvas.style.maxWidth = '640px';
                canvas.style.maxHeight = '480px';
                canvas.style.width = 'auto';
                canvas.style.height = 'auto';
            });
            
            modeSpans.forEach(span => {
                span.textContent = 'original';
            });
        }
        
        // 初始化
        document.querySelectorAll('.test-canvas').forEach(drawTestImage);
        setAllFitMode(); // 默认为 fit 模式
    </script>
</body>
</html>
