<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化测量系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            width: 800px;
            height: 600px;
            border: 2px solid #333;
            background-color: #000;
            position: relative;
            margin: 20px auto;
        }
        
        .canvas-layer {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            aspect-ratio: 640/480;
            object-fit: contain;
            border: 1px solid #ff0000;
        }
        
        .overlay-layer {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            aspect-ratio: 640/480;
            object-fit: contain;
            border: 1px solid #00ff00;
            pointer-events: auto;
            cursor: crosshair;
        }
        
        .origin-marker {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: #ff6b35;
            border: 2px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 20;
        }
        
        .measurement-point {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: #3b82f6;
            border: 2px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 10;
        }
        
        .info {
            text-align: center;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>简化测量系统测试</h1>
    
    <div class="info">
        <p>容器尺寸: 800x600px，原始图像尺寸: 640x480px (4:3 横纵比)</p>
        <p>红色边框: Canvas 层，绿色边框: 覆盖层，橙色圆点: 原点（应该在底部中心）</p>
        <p>点击覆盖层添加测量点</p>
    </div>
    
    <div class="controls">
        <button onclick="clearPoints()">清除所有点</button>
        <button onclick="showOrigin()">显示/隐藏原点</button>
        <button onclick="addCenterPoint()">添加中心点</button>
    </div>
    
    <div class="container" id="container">
        <!-- Canvas 层 -->
        <canvas 
            id="test-canvas" 
            class="canvas-layer"
            width="640" 
            height="480"
        ></canvas>
        
        <!-- 覆盖层 -->
        <div 
            id="overlay" 
            class="overlay-layer"
            onclick="handleOverlayClick(event)"
        >
            <!-- 原点标记 -->
            <div id="origin-marker" class="origin-marker" style="display: none;"></div>
            <!-- 测量点将动态添加到这里 -->
        </div>
    </div>
    
    <div class="info">
        <p>覆盖层尺寸: <span id="overlay-size">未知</span></p>
        <p>原点位置: <span id="origin-position">未知</span></p>
        <p>点击数: <span id="click-count">0</span></p>
    </div>

    <script>
        let clickCount = 0;
        let originVisible = false;
        
        // 绘制测试图像到 canvas
        function drawTestImage() {
            const canvas = document.getElementById('test-canvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            ctx.fillStyle = '#333';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制网格
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 1;
            for (let x = 0; x < canvas.width; x += 80) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y < canvas.height; y += 60) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
            
            // 绘制原点标记在 Canvas 上（底部中心）
            ctx.fillStyle = '#ff6b35';
            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height - 1, 8, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制中心标记
            ctx.fillStyle = '#00ff00';
            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height / 2, 6, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制文字
            ctx.fillStyle = '#ffffff';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('640x480 测试图像', canvas.width / 2, 30);
            ctx.fillText('橙色点: 原点位置', canvas.width / 2, canvas.height - 30);
        }
        
        // 更新覆盖层尺寸显示
        function updateOverlayInfo() {
            const overlay = document.getElementById('overlay');
            const rect = overlay.getBoundingClientRect();
            document.getElementById('overlay-size').textContent = 
                `${rect.width.toFixed(1)} x ${rect.height.toFixed(1)}`;
            
            // 计算原点在覆盖层坐标系中的位置（底部中心）
            const originX = rect.width / 2;
            const originY = rect.height - 1;
            document.getElementById('origin-position').textContent = 
                `(${originX.toFixed(1)}, ${originY.toFixed(1)})`;
            
            // 更新原点标记位置
            const originMarker = document.getElementById('origin-marker');
            originMarker.style.left = originX + 'px';
            originMarker.style.top = originY + 'px';
        }
        
        // 处理覆盖层点击
        function handleOverlayClick(event) {
            const overlay = event.currentTarget;
            const rect = overlay.getBoundingClientRect();
            
            // 计算相对于覆盖层的坐标
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // 直接使用覆盖层坐标，不需要转换
            addMeasurementPoint(x, y);
            
            clickCount++;
            document.getElementById('click-count').textContent = clickCount;
        }
        
        // 添加测量点
        function addMeasurementPoint(x, y) {
            const overlay = document.getElementById('overlay');
            const point = document.createElement('div');
            point.className = 'measurement-point';
            point.style.left = x + 'px';
            point.style.top = y + 'px';
            point.title = `覆盖层坐标: (${x.toFixed(1)}, ${y.toFixed(1)})`;
            
            overlay.appendChild(point);
        }
        
        // 清除所有点
        function clearPoints() {
            const overlay = document.getElementById('overlay');
            const points = overlay.querySelectorAll('.measurement-point');
            points.forEach(point => point.remove());
            clickCount = 0;
            document.getElementById('click-count').textContent = clickCount;
        }
        
        // 显示/隐藏原点
        function showOrigin() {
            const originMarker = document.getElementById('origin-marker');
            originVisible = !originVisible;
            originMarker.style.display = originVisible ? 'block' : 'none';
        }
        
        // 添加中心点
        function addCenterPoint() {
            const overlay = document.getElementById('overlay');
            const rect = overlay.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            addMeasurementPoint(centerX, centerY);
            clickCount++;
            document.getElementById('click-count').textContent = clickCount;
        }
        
        // 初始化
        drawTestImage();
        updateOverlayInfo();
        
        // 监听窗口大小变化
        window.addEventListener('resize', updateOverlayInfo);
        
        // 使用 ResizeObserver 监听覆盖层尺寸变化
        const overlay = document.getElementById('overlay');
        const resizeObserver = new ResizeObserver(updateOverlayInfo);
        resizeObserver.observe(overlay);
    </script>
</body>
</html>
