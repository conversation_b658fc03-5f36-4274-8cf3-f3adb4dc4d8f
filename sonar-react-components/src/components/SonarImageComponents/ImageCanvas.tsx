import React, { useRef, useCallback, useEffect } from 'react';
import { SonarMetadata } from '../../lib/mjpeg-stream';
import { MeasurementOverlay, MeasurementState } from '../measurement';
import { SonarMeasurementConfig } from '../../lib/sonar-geometry';
import { ConnectionStatus } from '../../lib/hooks/useConnectionManager';

// 叠加层配置接口
interface OverlayConfig {
  showFrameCount?: boolean;
  showTimestamp?: boolean;
  showSonarParams?: boolean;
}

// 组件属性接口
export interface ImageCanvasProps {
  // 基础数据
  currentImageSrc: string | null;
  pausedImageSrc?: string | null;
  connectionStatus: ConnectionStatus;
  frameCount: number;
  currentSonarMetadata: SonarMetadata["SonarParams"] | null;
  
  // 叠加层配置
  overlayConfig?: OverlayConfig;
  theme: "light" | "dark";
  debug: boolean;
  
  // 尺寸和缩放
  imageMetrics: {width: number, height: number, range: number} | null;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  scaleMode?: 'fit' | 'original';
  
  // 测量功能
  enableMeasurement: boolean;
  measurementState: MeasurementState;
  onMeasurementStateChange: React.Dispatch<React.SetStateAction<MeasurementState>>;
  finalMeasurementConfig: SonarMeasurementConfig;
  
  // 事件处理
  onImageLoad?: () => void;
}

// 默认叠加层配置
const defaultOverlayConfig: OverlayConfig = {
  showFrameCount: true,
  showTimestamp: false,
  showSonarParams: true,
};

/**
 * 图像画布组件
 * 负责图像显示和Canvas绘制逻辑
 */
export const ImageCanvas: React.FC<ImageCanvasProps> = ({
  currentImageSrc,
  pausedImageSrc,
  connectionStatus,
  frameCount,
  currentSonarMetadata,
  overlayConfig,
  theme,
  debug,
  imageMetrics,
  canvasRef,
  scaleMode = 'fit',
  enableMeasurement,
  measurementState,
  onMeasurementStateChange,
  finalMeasurementConfig,
  onImageLoad,
}) => {
  const imageRef = useRef<HTMLImageElement>(null);

  // 将图像绘制到 canvas 上（同步传递声呐数据）
  const drawImageToCanvas = useCallback((
    imageSrc: string,
    currentFrameCount: number,
    sonarMetadata: SonarMetadata["SonarParams"],
    timestamp?: number
  ) => {
    const canvas = canvasRef.current;
    const img = imageRef.current;
    
    if (!canvas || !img) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    img.onload = () => {
      // 设置 canvas 内部分辨率为图像的实际像素尺寸（用于绘制）
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;

      // CSS 尺寸由 CSS Grid 和 aspect-ratio 自动处理，无需手动设置

      // 清除画布并绘制新图像
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);
      
      // 根据配置绘制叠加信息
      const config = { ...defaultOverlayConfig, ...overlayConfig };
      
      // 绘制帧数信息
      if (config.showFrameCount) {
        const frameText = `已接收: ${currentFrameCount} 帧`;
        const frameX = 12;
        const frameY = canvas.height - 12;
        
        // 绘制帧数文本
        ctx.save();
        ctx.font = '12px Arial, sans-serif';
        const frameTextMetrics = ctx.measureText(frameText);
        const frameTextWidth = frameTextMetrics.width;
        const frameTextHeight = 12;
        const framePadding = 4;
        
        // 绘制背景
        ctx.fillStyle = theme === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(31, 41, 55, 0.75)';
        ctx.fillRect(frameX - framePadding, frameY - frameTextHeight - framePadding, frameTextWidth + framePadding * 2, frameTextHeight + framePadding * 2);
        
        // 绘制文本
        ctx.fillStyle = theme === 'dark' ? '#d1d5db' : '#374151';
        ctx.fillText(frameText, frameX, frameY);
        ctx.restore();
      }
        
      // 绘制声呐信息
      if ((config.showTimestamp || config.showSonarParams) && sonarMetadata && Object.keys(sonarMetadata).length > 0) {
          const lines: string[] = [];
          
          // 时间戳信息
          if (config.showTimestamp && timestamp) {
            const date = new Date(timestamp);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
            lines.push(`时间: ${hours}:${minutes}:${seconds}.${milliseconds}`);
          }
          
          // 声呐参数信息
          if (config.showSonarParams) {
            if (sonarMetadata.range !== undefined) {
              lines.push(`量程: ${sonarMetadata.range.toFixed(2)}米`);
            }
            if (sonarMetadata.pingPeriod) {
              lines.push(`发射周期: ${Math.round(sonarMetadata.pingPeriod)} ms`);
            }
            if (sonarMetadata.beamNum) {
              lines.push(`波束数: ${sonarMetadata.beamNum}`);
            }
            if (sonarMetadata.velocity) {
              lines.push(`声速: ${sonarMetadata.velocity} m/s`);
            }
            if (sonarMetadata.gain !== undefined) {
              lines.push(`增益: ${sonarMetadata.gain}`);
            }
          }
          
          if (lines.length > 0) {
            const lineHeight = 16;
            const padding = 8;
            const rightMargin = 8;
            const bottomMargin = 40;
            
            const startY = canvas.height - bottomMargin - (lines.length - 1) * lineHeight;
            
            lines.forEach((line, index) => {
              const textMetrics = ctx.measureText(line);
              const x = canvas.width - textMetrics.width - rightMargin - padding;
              const y = startY + index * lineHeight;
              
              // 绘制声呐信息文本
              ctx.save();
              ctx.font = '12px Arial, sans-serif';
              const lineMetrics = ctx.measureText(line);
              const lineWidth = lineMetrics.width;
              const textHeight = 12;
              const linePadding = 4;
              
              // 绘制背景
              ctx.fillStyle = theme === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(31, 41, 55, 0.75)';
              ctx.fillRect(x - linePadding, y - textHeight - linePadding, lineWidth + linePadding * 2, textHeight + linePadding * 2);
              
              // 绘制文本
              ctx.fillStyle = theme === 'dark' ? '#d1d5db' : '#374151';
              ctx.fillText(line, x, y);
              ctx.restore();
            });
          }
        }
      
      // 释放 URL 资源
      URL.revokeObjectURL(imageSrc);
      
      if (onImageLoad) {
        onImageLoad();
      }
    };
    
    img.src = imageSrc;
  }, [theme, overlayConfig, canvasRef, onImageLoad]);

  // 计算图像在 canvas 中的实际显示区域
  const [imageDisplayArea, setImageDisplayArea] = React.useState<{
    width: number;
    height: number;
    offsetX: number;
    offsetY: number;
  } | null>(null);

  // 监听 Canvas 尺寸变化，计算图像实际显示区域
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageMetrics) return;

    const updateImageDisplayArea = () => {
      const canvasRect = canvas.getBoundingClientRect();
      const canvasWidth = canvasRect.width;
      const canvasHeight = canvasRect.height;

      // 计算图像的宽高比
      const imageAspectRatio = imageMetrics.width / imageMetrics.height;
      const canvasAspectRatio = canvasWidth / canvasHeight;

      let displayWidth, displayHeight, offsetX, offsetY;

      if (canvasAspectRatio > imageAspectRatio) {
        // Canvas 更宽，图像高度填满，宽度居中
        displayHeight = canvasHeight;
        displayWidth = displayHeight * imageAspectRatio;
        offsetX = (canvasWidth - displayWidth) / 2;
        offsetY = 0;
      } else {
        // Canvas 更高，图像宽度填满，高度居中
        displayWidth = canvasWidth;
        displayHeight = displayWidth / imageAspectRatio;
        offsetX = 0;
        offsetY = (canvasHeight - displayHeight) / 2;
      }

      setImageDisplayArea({
        width: displayWidth,
        height: displayHeight,
        offsetX,
        offsetY
      });
    };

    // 初始更新
    updateImageDisplayArea();

    // 使用 ResizeObserver 监听尺寸变化
    const resizeObserver = new ResizeObserver(updateImageDisplayArea);
    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.disconnect();
    };
  }, [canvasRef, imageMetrics]);

  // 当有新图像数据时自动绘制到canvas
  useEffect(() => {
    if (currentImageSrc && connectionStatus === "connected" && currentSonarMetadata) {
      drawImageToCanvas(
        currentImageSrc,
        frameCount,
        currentSonarMetadata,
        Date.now()
      );
    }
  }, [currentImageSrc, connectionStatus, frameCount, currentSonarMetadata, drawImageToCanvas]);

  // 处理缩放模式切换时的坐标转换
  const [previousScaleMode, setPreviousScaleMode] = React.useState<'fit' | 'original'>(scaleMode);
  const [previousImageDisplayArea, setPreviousImageDisplayArea] = React.useState<typeof imageDisplayArea>(null);

  useEffect(() => {
    // 如果缩放模式发生变化且有测量点存在，需要转换坐标
    if (previousScaleMode !== scaleMode &&
        measurementState.points.length > 0 &&
        imageDisplayArea &&
        previousImageDisplayArea &&
        imageMetrics) {

      const convertedPoints = measurementState.points.map(point => {
        let newPixelCoordinate;

        if (previousScaleMode === 'fit' && scaleMode === 'original') {
          // 从 fit 模式转换到 original 模式
          // fit 模式下的坐标是基于显示区域的，需要转换为原始图像坐标
          const scaleX = imageMetrics.width / previousImageDisplayArea.width;
          const scaleY = imageMetrics.height / previousImageDisplayArea.height;

          newPixelCoordinate = {
            x: point.pixelCoordinate.x * scaleX,
            y: point.pixelCoordinate.y * scaleY
          };
        } else if (previousScaleMode === 'original' && scaleMode === 'fit') {
          // 从 original 模式转换到 fit 模式
          // original 模式下的坐标是基于原始图像的，需要转换为显示区域坐标
          const scaleX = imageDisplayArea.width / imageMetrics.width;
          const scaleY = imageDisplayArea.height / imageMetrics.height;

          newPixelCoordinate = {
            x: point.pixelCoordinate.x * scaleX,
            y: point.pixelCoordinate.y * scaleY
          };
        } else {
          // 相同模式，不需要转换
          newPixelCoordinate = point.pixelCoordinate;
        }

        // 创建新的测量点，保持其他属性不变
        return {
          ...point,
          pixelCoordinate: newPixelCoordinate,
          // 注意：极坐标和直角坐标会在 SonarGeometry 中重新计算
        };
      });

      // 更新测量状态
      onMeasurementStateChange({
        ...measurementState,
        points: convertedPoints,
        // 清除结果，让它们重新计算
        results: []
      });
    }

    // 更新前一次的状态
    setPreviousScaleMode(scaleMode);
    setPreviousImageDisplayArea(imageDisplayArea);
  }, [scaleMode, imageDisplayArea, measurementState, onMeasurementStateChange, imageMetrics, previousScaleMode, previousImageDisplayArea]);

  // 不再使用坐标转换器，通过 CSS 确保 MeasurementOverlay 和 Canvas 完全重叠

  // 显示的图像源（连接状态下用当前图像，暂停状态下用暂停的图像）
  const displayImageSrc = connectionStatus === "paused" ? pausedImageSrc : currentImageSrc;



  if (!displayImageSrc) {
    return (
      <div className="flex flex-col justify-center items-center w-full h-full text-center p-5 bg-black text-gray-300">
        <p>等待图像数据...</p>
        <div className="mt-4 w-8 h-8 border-4 border-blue-200 border-l-blue-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      {/* 隐藏的 img 元素用于加载图像 */}
      <img
        ref={imageRef}
        style={{ display: 'none' }}
        alt={connectionStatus === "paused" ? "Paused Sonar Image" : "Sonar MJPEG Stream"}
        src={displayImageSrc}
        onError={(e) => {
          console.error("图像加载错误:", e);
          if (debug) {
            const img = e.target as HTMLImageElement;
            console.log("图像URL:", img.src);
            console.log("图像naturalWidth:", img.naturalWidth);
            console.log("图像naturalHeight:", img.naturalHeight);
          }
        }}
      />

      {/* 根据缩放模式使用不同的布局策略 */}
      {scaleMode === 'fit' ? (
        // fit 模式：Canvas 和 MeasurementOverlay 完全重叠
        <div className="w-full h-full relative">
          {/* Canvas 层 */}
          <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full h-full"
            style={{
              aspectRatio: imageMetrics ? `${imageMetrics.width}/${imageMetrics.height}` : '4/3',
              objectFit: 'contain'
            }}
          />

          {/* 测量覆盖层 - 精确对齐到图像的实际显示区域 */}
          {enableMeasurement && imageMetrics && imageDisplayArea && (
            <div
              className="absolute"
              style={{
                left: `${imageDisplayArea.offsetX}px`,
                top: `${imageDisplayArea.offsetY}px`,
                width: `${imageDisplayArea.width}px`,
                height: `${imageDisplayArea.height}px`
              }}
            >
              <MeasurementOverlay
                enabled={enableMeasurement}
                measurementState={measurementState}
                onMeasurementStateChange={onMeasurementStateChange}
                sonarConfig={finalMeasurementConfig}
                imageWidth={imageMetrics.width}
                imageHeight={imageMetrics.height}
                range={imageMetrics.range}
                coordinateTransformer={undefined} // 不使用坐标转换器，直接 1:1 映射
                className="w-full h-full"
              />
            </div>
          )}
        </div>
      ) : (
        // original 模式：使用居中布局，限制最大尺寸
        <div className="grid place-items-center w-full h-full">
          <div className="relative max-w-full max-h-full">
            <canvas
              ref={canvasRef}
              className="block"
              style={{
                aspectRatio: imageMetrics ? `${imageMetrics.width}/${imageMetrics.height}` : '4/3',
                maxWidth: `${imageMetrics?.width || 640}px`,
                maxHeight: `${imageMetrics?.height || 480}px`,
                width: 'auto',
                height: 'auto'
              }}
            />

            {/* 测量覆盖层 - original 模式下也使用相同的简化方法 */}
            {enableMeasurement && imageMetrics && (
              <div
                className="absolute inset-0"
                style={{
                  maxWidth: `${imageMetrics.width}px`,
                  maxHeight: `${imageMetrics.height}px`,
                  width: 'auto',
                  height: 'auto',
                  aspectRatio: `${imageMetrics.width}/${imageMetrics.height}`
                }}
              >
                <MeasurementOverlay
                  enabled={enableMeasurement}
                  measurementState={measurementState}
                  onMeasurementStateChange={onMeasurementStateChange}
                  sonarConfig={finalMeasurementConfig}
                  imageWidth={imageMetrics.width}
                  imageHeight={imageMetrics.height}
                  range={imageMetrics.range}
                  coordinateTransformer={undefined} // 不使用坐标转换器，直接 1:1 映射
                  className="w-full h-full"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* 暂停状态提示 */}
      {connectionStatus === "paused" && (
        <div className="absolute top-2 left-2 bg-yellow-600/80 backdrop-blur-sm rounded px-2 py-1 text-xs text-white">
          已暂停
        </div>
      )}
    </div>
  );
};