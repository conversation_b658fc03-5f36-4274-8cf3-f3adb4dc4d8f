import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ImageCanvas } from '../SonarImageComponents/ImageCanvas';

// Mock the measurement overlay
jest.mock('../../components/measurement', () => ({
  MeasurementOverlay: ({ className }: any) => (
    <div data-testid="measurement-overlay" className={className}>
      Measurement Overlay
    </div>
  ),
}));

describe('ImageCanvas Fit Mode', () => {
  const defaultProps = {
    currentImageSrc: 'data:image/jpeg;base64,test',
    pausedImageSrc: null,
    connectionStatus: 'connected' as const,
    frameCount: 10,
    currentSonarMetadata: {
      width: 640,
      height: 480,
      range: 100,
    },
    overlayConfig: {},
    theme: 'dark' as const,
    debug: false,
    imageMetrics: { width: 640, height: 480, range: 100 },
    canvasRef: { current: null },
    enableMeasurement: false,
    measurementState: { mode: 'none' as const, isActive: false, points: [], results: [] },
    onMeasurementStateChange: jest.fn(),
    finalMeasurementConfig: {},
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should apply fit mode styles correctly', () => {
    render(
      <ImageCanvas
        {...defaultProps}
        scaleMode="fit"
      />
    );

    const container = document.querySelector('.w-full.h-full.flex.items-center.justify-center');
    expect(container).toBeInTheDocument();
  });

  it('should apply original mode styles correctly', () => {
    render(
      <ImageCanvas
        {...defaultProps}
        scaleMode="original"
      />
    );

    const container = document.querySelector('.max-w-full.max-h-full');
    expect(container).toBeInTheDocument();
  });

  it('should set correct canvas styles for fit mode', () => {
    const mockCanvasRef = { current: document.createElement('canvas') };
    
    render(
      <ImageCanvas
        {...defaultProps}
        scaleMode="fit"
        canvasRef={mockCanvasRef}
      />
    );

    const canvas = document.querySelector('canvas');
    expect(canvas).toBeInTheDocument();
    
    // Check that the canvas has the correct aspect ratio
    const computedStyle = window.getComputedStyle(canvas!);
    expect(canvas).toHaveStyle('aspect-ratio: 640/480');
  });

  it('should set correct canvas styles for original mode', () => {
    const mockCanvasRef = { current: document.createElement('canvas') };
    
    render(
      <ImageCanvas
        {...defaultProps}
        scaleMode="original"
        canvasRef={mockCanvasRef}
      />
    );

    const canvas = document.querySelector('canvas');
    expect(canvas).toBeInTheDocument();
    
    // Check that the canvas has the correct aspect ratio and max dimensions
    expect(canvas).toHaveStyle('aspect-ratio: 640/480');
    expect(canvas).toHaveStyle('max-width: 640px');
    expect(canvas).toHaveStyle('max-height: 480px');
  });

  it('should use default aspect ratio when imageMetrics is null', () => {
    const mockCanvasRef = { current: document.createElement('canvas') };
    
    render(
      <ImageCanvas
        {...defaultProps}
        imageMetrics={null}
        canvasRef={mockCanvasRef}
      />
    );

    const canvas = document.querySelector('canvas');
    expect(canvas).toHaveStyle('aspect-ratio: 4/3');
  });

  it('should render measurement overlay when enabled', () => {
    render(
      <ImageCanvas
        {...defaultProps}
        enableMeasurement={true}
      />
    );

    expect(screen.getByTestId('measurement-overlay')).toBeInTheDocument();
  });

  it('should not render measurement overlay when disabled', () => {
    render(
      <ImageCanvas
        {...defaultProps}
        enableMeasurement={false}
      />
    );

    expect(screen.queryByTestId('measurement-overlay')).not.toBeInTheDocument();
  });
});
