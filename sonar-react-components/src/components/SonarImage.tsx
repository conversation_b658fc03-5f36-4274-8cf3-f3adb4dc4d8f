import React, { useState, useEffect, useCallback, useMemo } from "react";
import { SonarMetadata } from "../lib/mjpeg-stream";
import {
  useVideoRecorder,
  useConnectionManager,
  useMeasurementController,
  useImageMetrics,
  useToolbarVisibility,
  ScaleConfig,
} from "../lib/hooks";
import { SonarMeasurementConfig } from "../lib/sonar-geometry";
import {
  ConnectionStatusDisplay,
  ImageCanvas,
  ToolbarContainer,
  RecordingToolbar,
  StatusIndicators,
} from "./SonarImageComponents";

// 叠加层配置接口
interface OverlayConfig {
  showFrameCount?: boolean;
  showTimestamp?: boolean;
  showSonarParams?: boolean;
}

// 组件属性定义
interface SonarImageProps {
  sonarIP: string;
  streamEndpoint?: string;
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onMetaData?: (metadata: SonarMetadata) => void;
  debug?: boolean;
  theme?: "light" | "dark";
  autoStart?: boolean;
  overlayConfig?: OverlayConfig;
  // 测量功能配置
  measurementConfig?: SonarMeasurementConfig;
  enableMeasurement?: boolean;
  // 缩放功能配置
  scaleConfig?: ScaleConfig;
}

/**
 * SonarImage组件 - 重构版本
 * 使用Hook和子组件实现，大幅简化主组件逻辑
 */
const SonarImage: React.FC<SonarImageProps> = ({
  sonarIP,
  streamEndpoint = "/stream",
  onError,
  onConnect,
  onDisconnect,
  onMetaData,
  debug = false,
  theme = "dark",
  autoStart = false,
  overlayConfig,
  measurementConfig,
  enableMeasurement = false,
  scaleConfig,
}) => {
  // 录制相关状态
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  
  // 使用连接管理Hook
  const connectionManager = useConnectionManager({
    sonarIP,
    streamEndpoint,
    debug,
    autoStart,
    onError,
    onConnect,
    onDisconnect,
    onMetaData,
    onFrame: () => {
      // 这里可以添加额外的帧处理逻辑
    },
  });

  // 使用图像尺寸管理Hook
  const imageMetrics = useImageMetrics({
    currentSonarMetadata: connectionManager.currentSonarMetadata,
  });

  // 简化的缩放控制 - 仅用于工具栏显示
  const [currentScaleMode, setCurrentScaleMode] = useState<'fit' | 'original'>('fit');
  const finalScaleConfig = useMemo(() => {
    return { mode: currentScaleMode, allowModeToggle: true, ...scaleConfig };
  }, [currentScaleMode, scaleConfig]);

  const handleScaleModeChange = useCallback(() => {
    if (finalScaleConfig.allowModeToggle) {
      setCurrentScaleMode(prev => prev === 'fit' ? 'original' : 'fit');
    }
  }, [finalScaleConfig.allowModeToggle]);

  // 使用测量控制Hook
  const measurementController = useMeasurementController({
    enableMeasurement,
    measurementConfig,
  });

  // 使用录制功能Hook
  const videoRecorder = useVideoRecorder({
    mimeType: 'video/mp4',
    videoBitsPerSecond: 2500000,
    sonarIP,
  });

  // 使用工具栏可见性管理Hook
  const toolbarVisibility = useToolbarVisibility({
    isRecording: videoRecorder.isRecording,
  });

  // 格式化录制时间显示
  const formatRecordingTime = useCallback((seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    }
  }, []);

  // 录制控制函数
  const handleStartRecording = useCallback(async () => {
    const canvas = imageMetrics.canvasRef.current;
    if (!canvas) {
      console.error('Canvas 元素不可用');
      return;
    }
    
    const success = await videoRecorder.startRecording(canvas);
    if (success) {
      console.log('录制已开始');
    }
  }, [videoRecorder, imageMetrics.canvasRef]);

  const handleStopRecording = useCallback(async () => {
    const blob = await videoRecorder.stopRecording();
    if (blob) {
      setRecordedBlob(blob);
      console.log('录制已停止，文件大小:', blob.size);
    }
  }, [videoRecorder]);

  const handleDownloadRecording = useCallback(async () => {
    if (recordedBlob) {
      videoRecorder.downloadRecording(recordedBlob);
      setRecordedBlob(null);
    }
  }, [recordedBlob, videoRecorder]);

  // 容器点击处理函数
  const handleContainerClick = useCallback(() => {
    // 如果未连接且未暂停，直接处理连接逻辑
    if (connectionManager.connectionStatus !== "connected" && connectionManager.connectionStatus !== "paused") {
      connectionManager.handleImageClick();
      return;
    }
    
    // 如果已连接或暂停且测量模式不活跃，则不处理（让MeasurementOverlay处理）
    if (enableMeasurement && measurementController.measurementState.mode !== 'none') {
      return;
    }
    
    // 其他情况不处理点击（已连接或暂停状态）
  }, [connectionManager, enableMeasurement, measurementController.measurementState.mode]);

  // 键盘事件处理函数
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.code === 'Space') {
      event.preventDefault();
      connectionManager.handleImageClick();
    }
  }, [connectionManager]);

  // 监控连接状态变化，控制录制暂停/恢复
  useEffect(() => {
    if (videoRecorder.isRecording) {
      if (connectionManager.connectionStatus === "connected") {
        videoRecorder.resumeTimer();
      } else {
        videoRecorder.pauseTimer();
      }
    }
  }, [connectionManager.connectionStatus, videoRecorder]);

  // 添加键盘事件监听器
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // 获取主题相关的样式
  const themeStyles = useMemo(() => {
    if (theme === "light") {
      return {
        container: "bg-black border-gray-300",
        standby: "bg-gray-50 text-gray-700",
        connecting: "bg-gray-100 text-blue-700",
        connected: "bg-black",
        statusBar: "bg-gray-800 bg-opacity-75 text-white",
        infoPanel: "bg-gray-800 bg-opacity-75 text-white",
        error: "text-red-700",
      };
    } else {
      return {
        container: "bg-black border-gray-700",
        standby: "bg-black text-gray-300",
        connecting: "bg-gray-900 text-blue-400",
        connected: "bg-black",
        statusBar: "bg-black bg-opacity-80 text-gray-200",
        infoPanel: "bg-black bg-opacity-80 text-gray-200",
        error: "text-red-400",
      };
    }
  }, [theme]);

  // 根据连接状态渲染内容
  const renderMainContent = useCallback(() => {
    if (connectionManager.connectionStatus === "standby" || connectionManager.connectionStatus === "connecting") {
      return (
        <ConnectionStatusDisplay
          connectionStatus={connectionManager.connectionStatus}
          error={connectionManager.error}
          themeStyles={themeStyles}
        />
      );
    }

    if (connectionManager.connectionStatus === "connected" || connectionManager.connectionStatus === "paused") {
      return (
        <ImageCanvas
          currentImageSrc={connectionManager.currentImageSrc}
          pausedImageSrc={connectionManager.pausedImageSrc}
          connectionStatus={connectionManager.connectionStatus}
          frameCount={connectionManager.frameCount}
          currentSonarMetadata={connectionManager.currentSonarMetadata}
          overlayConfig={overlayConfig}
          theme={theme}
          debug={debug}
          imageMetrics={imageMetrics.imageMetrics}
          canvasRef={imageMetrics.canvasRef}
          scaleMode={currentScaleMode}
          enableMeasurement={enableMeasurement}
          measurementState={measurementController.measurementState}
          onMeasurementStateChange={measurementController.setMeasurementState}
          finalMeasurementConfig={measurementController.finalMeasurementConfig}
        />
      );
    }

    return null;
  }, [
    connectionManager,
    themeStyles,
    overlayConfig,
    theme,
    debug,
    imageMetrics,
    currentScaleMode,
    enableMeasurement,
    measurementController,
  ]);

  return (
    <div
      ref={imageMetrics.containerRef}
      className={`relative overflow-hidden flex justify-center items-center transition-all duration-300 ease-in-out w-full h-full ${themeStyles.container} ${
        connectionManager.connectionStatus === "connected" ? themeStyles.connected : ""
      } ${toolbarVisibility.isMouseIdle ? "cursor-none" : ""}`}
      onClick={handleContainerClick}
      onMouseMove={toolbarVisibility.handleMouseMove}
    >
      {renderMainContent()}

      {/* 工具栏容器 */}
      {videoRecorder.isSupported && (
        <ToolbarContainer
          connectionStatus={connectionManager.connectionStatus}
          isRecording={videoRecorder.isRecording}
          showToolbar={toolbarVisibility.showRecordingToolbar}
          onMouseEnter={toolbarVisibility.handleToolbarMouseEnter}
          onMouseLeave={toolbarVisibility.handleToolbarMouseLeave}
        >
          <RecordingToolbar
            connectionStatus={connectionManager.connectionStatus}
            videoRecorder={videoRecorder}
            recordedBlob={recordedBlob}
            enableMeasurement={enableMeasurement}
            showMeasurementToolbar={measurementController.showMeasurementToolbar}
            currentScaleMode={currentScaleMode}
            finalScaleConfig={finalScaleConfig}
            onPause={connectionManager.handlePause}
            onResumeFromPause={connectionManager.handleResumeFromPause}
            onStopFromPause={connectionManager.handleStopFromPause}
            onImageClick={connectionManager.handleImageClick}
            onStartRecording={handleStartRecording}
            onStopRecording={handleStopRecording}
            onDownloadRecording={handleDownloadRecording}
            onToggleMeasurementToolbar={measurementController.handleToggleMeasurementToolbar}
            onScaleModeChange={handleScaleModeChange}
            formatRecordingTime={formatRecordingTime}
          />
        </ToolbarContainer>
      )}

      {/* 状态指示器 */}
      <StatusIndicators
        connectionStatus={connectionManager.connectionStatus}
        recordingError={videoRecorder.error}
        debug={debug}
        enableMeasurement={enableMeasurement}
        measurementState={measurementController.measurementState}
        showMeasurementToolbar={measurementController.showMeasurementToolbar}
        onMeasurementModeChange={measurementController.handleMeasurementModeChange}
        onClearMeasurements={measurementController.handleClearMeasurements}
        themeStyles={themeStyles}
      />
    </div>
  );
};

export default SonarImage;